"use client";
import Link from "next/link";
import { useEffect, useMemo, useState } from "react";
import { FaSortUp, FaSortDown } from "react-icons/fa";

import { AppButton } from "@/app/components/app-button";
import { AppInput } from "@/app/components/app-input";
import { AppSpinner } from "@/app/components/app-spinner";
import { Body1, Body3, Heading } from "@/app/components/app-typography";

import type { Job } from "@/app/services/jobs.hooks";
import { useGetJobs } from "@/app/services/jobs.hooks";
import type { BrokenPropShow } from "@/app/types/types";
import { searchObject } from "@/lib/utils";
import { formatDateString } from "@/utils/format";
import axios from "axios";

// Component for job status and broken properties
const JobStatusSection = ({
  job,
  index,
  showBrokenPropDropdown,
  onToggleBrokenProps,
}: {
  job: Job;
  index: number;
  showBrokenPropDropdown: BrokenPropShow[];
  onToggleBrokenProps: (index: number) => void;
}) => (
  <div>
    {job.testsFailed != null && job.testsCoverage != null ? (
      <div className="mb-1 flex w-full gap-6">
        <Body3 color="primary" className="flex items-center gap-2">
          <span>{job.testsFailed == 0 ? "🟢" : "🔴"}</span>
          Failed: {job.testsFailed} - Coverage: {job.testsCoverage} - Status:{" "}
          {job.status}
          {job.progress && ` - Progress: ${job.progress}%`}
          {job.eta &&
            job?.progress < 100 &&
            ` - ETA: ${job.eta !== "0m" ? job.eta : "<1m"}`}
        </Body3>
      </div>
    ) : job.status === "ERROR" ? (
      <div className="mb-1">
        <Body3 color="primary">Status: {job.status}</Body3>
      </div>
    ) : null}

    {showBrokenPropDropdown.length > 0 &&
      job.brokenProperties &&
      job.brokenProperties.length > 0 && (
        <div>
          <button
            onClick={() => onToggleBrokenProps(index)}
            className="text-fore-neutral-primary hover:text-accent-primary transition-colors"
          >
            <Body3>
              {showBrokenPropDropdown.find((el) => el.id === index)?.show
                ? "Hide"
                : "Show"}{" "}
              broken Props
            </Body3>
          </button>
          {showBrokenPropDropdown.find((el) => el.id === index)?.show && (
            <select className="w-auto rounded border border-stroke-neutral-decorative bg-back-neutral-primary p-2 text-fore-neutral-primary mt-2">
              {job.brokenProperties.map((prop, propIndex) => (
                <option
                  key={`${job.id}-${prop.brokenProperty}-${propIndex}`}
                  value={prop.brokenProperty}
                  className="text-fore-neutral-primary"
                >
                  {prop.brokenProperty}
                </option>
              ))}
            </select>
          )}
        </div>
      )}
  </div>
);

// Component for individual job card
const JobCard = ({
  job,
  index,
  isEditing,
  existingLabel,
  showBrokenPropDropdown,
  onToggleEdit,
  onLabelChange,
  onKeyPress,
  onToggleBrokenProps,
}: {
  job: Job;
  index: number;
  isEditing: boolean;
  existingLabel: string;
  showBrokenPropDropdown: BrokenPropShow[];
  onToggleEdit: () => void;
  onLabelChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onKeyPress: (e: React.KeyboardEvent<HTMLInputElement>) => void;
  onToggleBrokenProps: (index: number) => void;
}) => (
  <div className="flex flex-col py-5 px-6 justify-between bg-back-neutral-tertiary rounded-lg border border-stroke-neutral-decorative">
    <div className="mb-2 w-full flex flex-wrap items-center gap-2">
      <EditableJobLabel
        job={job}
        isEditing={isEditing}
        existingLabel={existingLabel}
        onToggleEdit={onToggleEdit}
        onLabelChange={onLabelChange}
        onKeyPress={onKeyPress}
      />
      <Body1 color="primary">
        - {job.fuzzer} - {job.repoName} {job.ref} - Created{" "}
        {formatDateString(job.createdAt)} - Updated{" "}
        {formatDateString(job.updatedAt)}
      </Body1>
    </div>

    <JobStatusSection
      job={job}
      index={index}
      showBrokenPropDropdown={showBrokenPropDropdown}
      onToggleBrokenProps={onToggleBrokenProps}
    />

    <div className="flex w-full gap-6">
      <JobLink
        url={`/dashboard/jobs/${job.id}`}
        label="View Details"
        sameWindow
      />
      {(job.status === "SUCCESS" ||
        job.status === "ERROR" ||
        job.status === "STOPPED") && (
        <JobLink
          url={`/dashboard/jobs/${job.id}/report`}
          label="View Report"
          sameWindow
        />
      )}
    </div>
  </div>
);

// Component for rendering links
const JobLink = ({
  url,
  label,
  sameWindow = false,
}: {
  url: string;
  label: string;
  sameWindow?: boolean;
}) => {
  if (!url) return null;
  return (
    <Link target={sameWindow ? "" : "_blank"} href={url}>
      <Body3
        color="secondary"
        className="hover:text-fore-neutral-primary transition-colors"
      >
        {label}
      </Body3>
    </Link>
  );
};

// Component for sort toggle button
const SortToggle = ({
  sortBy,
  sortDirection,
  onToggle,
}: {
  sortBy: string;
  sortDirection: "asc" | "desc";
  onToggle: () => void;
}) => (
  <AppButton
    variant="outline"
    onClick={onToggle}
    rightIcon={sortDirection === "asc" ? <FaSortUp /> : <FaSortDown />}
  >
    <Body3 color="secondary">Sorting by {sortBy}</Body3>
  </AppButton>
);

const FilterToggle = ({
  filterNoBroken,
  onToggle,
}: {
  filterNoBroken: boolean;
  onToggle: () => void;
}) => (
  <AppButton variant="outline" onClick={onToggle}>
    <Body3 color="secondary">
      {filterNoBroken
        ? "Showing only jobs with broken properties"
        : "Showing all jobs"}
    </Body3>
  </AppButton>
);

// Component for editable job label
const EditableJobLabel = ({
  job,
  isEditing,
  existingLabel,
  onToggleEdit,
  onLabelChange,
  onKeyPress,
}: {
  job: Job;
  isEditing: boolean;
  existingLabel: string;
  onToggleEdit: () => void;
  onLabelChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onKeyPress: (e: React.KeyboardEvent<HTMLInputElement>) => void;
}) => (
  <div
    onDoubleClick={onToggleEdit}
    className="cursor-pointer hover:bg-back-neutral-secondary rounded px-2 py-1 transition-colors"
  >
    {isEditing ? (
      <input
        value={existingLabel}
        onChange={onLabelChange}
        onKeyDown={onKeyPress}
        className="min-w-[120px] h-10 rounded-md px-4 w-full outline-none transition-all duration-200 font-bold text-base bg-transparent border border-stroke-neutral-decorative text-fore-neutral-primary hover:border-fore-neutral-secondary focus:border-accent-primary placeholder:text-fore-neutral-quaternary"
        placeholder="Job label"
      />
    ) : (
      <Body1 color="primary">{job.label || job.id.slice(0, 4)}</Body1>
    )}
  </div>
);

export const AllJobs = () => {
  const [isEditing, setIsEditing] = useState({});
  const [existingLabels, setExistingLabels] = useState({});
  const { data, isLoading, refetch, isRefetching } = useGetJobs();

  const [showBrokenPropDropdown, setShowBrokenPropDropdown] = useState<
    BrokenPropShow[]
  >([]);

  const buttonDisabled = isLoading || isRefetching;

  const [sortBy, setSortBy] = useState("createdAt");
  const [sortDirection, setSortDirection] = useState<"asc" | "desc">("desc");

  function toggleSortDirection() {
    setSortDirection(sortDirection === "asc" ? "desc" : "asc");
  }

  const [filterNoBroken, setFilterNoBroken] = useState(false);
  function toggleFilterBroken() {
    setFilterNoBroken(!filterNoBroken);
  }

  const [query, setQuery] = useState("");

  const sortedJobs = useMemo(() => {
    return (
      data
        ?.sort((jobA, jobB) => {
          const a = new Date(jobA[sortBy]).getTime();
          const b = new Date(jobB[sortBy]).getTime();
          return sortDirection === "asc" ? a - b : b - a;
        })
        // Keywords
        .filter((job) => {
          if (!query) return true;

          const queryWords = query.toLowerCase().split(/\s+/);
          return queryWords.every((word) => searchObject(job, word));
        })
        // Filter no broken props
        .filter((job) => (filterNoBroken ? job.testsFailed > 0 : true))
    );
  }, [sortBy, data, query, filterNoBroken, sortDirection]);

  useEffect(() => {
    if (sortedJobs && sortedJobs.length > 0) {
      setShowBrokenPropDropdown(
        sortedJobs.map((_, index) => ({
          id: index,
          show: false,
        }))
      );
    }
  }, [sortedJobs]);

  const showBrokenPropHandler = (index: number) => {
    setShowBrokenPropDropdown((prev) =>
      prev.map((trace) => {
        if (trace.id === index) {
          return {
            ...trace,
            show: !trace.show,
          };
        }
        return trace;
      })
    );
  };

  useEffect(() => {
    if (!data) return;

    const newIsEditing = {};
    const newExistingLabels = {};

    data.forEach((job) => {
      newIsEditing[job.id] = false;
      newExistingLabels[job.id] = job.label ? job.label : "";
    });

    setIsEditing(newIsEditing);
    setExistingLabels(newExistingLabels);
  }, [data]);

  const updateJob = async (id: string) => {
    const res = await axios({
      method: "POST",
      url: `/api/jobs/updateLabel`,
      data: {
        newLabel: existingLabels[id],
        jobId: id,
      },
    });
    if (res.data.data === "success") {
      refetch();
    }
  };

  const newLabelHandling = (
    id: string,
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    setExistingLabels({
      ...existingLabels,
      [id]: e.target.value,
    });
  };

  const handleKeyPress = (
    id: string,
    event: React.KeyboardEvent<HTMLInputElement>
  ) => {
    if (event.key === "Enter") {
      updateJob(id);
    }
  };

  return (
    <div className="mb-5 mt-12">
      <div className="mb-10 flex items-center justify-between">
        <div className="flex gap-4 items-center">
          <Heading color="primary">All Jobs</Heading>

          <SortToggle
            sortBy={sortBy}
            sortDirection={sortDirection}
            onToggle={toggleSortDirection}
          />
          <FilterToggle
            filterNoBroken={filterNoBroken}
            onToggle={toggleFilterBroken}
          />
        </div>

        <div className="flex gap-4">
          <AppButton onClick={refetch} disabled={buttonDisabled}>
            {buttonDisabled ? <AppSpinner /> : "Force Reload"}
          </AppButton>
        </div>
      </div>

      <div className="mb-5">
        <AppInput
          type="text"
          placeholder="Search names, properties, fuzzer, coverage… everything"
          value={query}
          onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
            setQuery(e.target.value)
          }
          className="w-full"
        />
      </div>

      {sortedJobs?.length > 0 && (
        <div className="flex flex-col gap-5">
          {sortedJobs.map((job, index) => (
            <JobCard
              key={job.id}
              job={job}
              index={index}
              isEditing={isEditing[job.id]}
              existingLabel={existingLabels[job.id]}
              showBrokenPropDropdown={showBrokenPropDropdown}
              onToggleEdit={() =>
                setIsEditing((prevState) => ({
                  ...prevState,
                  [job.id]: !prevState[job.id],
                }))
              }
              onLabelChange={(event) => newLabelHandling(job.id, event)}
              onKeyPress={(event) => handleKeyPress(job.id, event)}
              onToggleBrokenProps={showBrokenPropHandler}
            />
          ))}
        </div>
      )}
    </div>
  );
};
