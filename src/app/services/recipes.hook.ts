import { useQuery } from "@tanstack/react-query";
import axios from "axios";
import { useEffect, useState } from "react";

export interface Recipe {
  id: string;
  displayName: string;
  orgName: string;
  repoName: string;
  ref: string;
  fuzzer: string;
  fuzzerArgs: {
    target: string;
    prepareContracts: {
      target: string;
      replacement: string;
      targetContract: string;
    }[];
  };
}

// Mock data for recipes
const mockRecipes: Recipe[] = [
  {
    id: "recipe-1",
    displayName: "DeFi Protocol Fuzzing Recipe",
    orgName: "defi-protocol",
    repoName: "core-contracts",
    ref: "main",
    fuzzer: "medusa",
    fuzzerArgs: {
      target: "test/invariants/",
      prepareContracts: [
        {
          target: "address(0x123)",
          replacement: "address(mockToken)",
          targetContract: "MockERC20",
        },
        {
          target: "1000000",
          replacement: "INITIAL_SUPPLY",
          targetContract: "TokenVault",
        },
      ],
    },
  },
  {
    id: "recipe-2",
    displayName: "NFT Marketplace Security Suite",
    orgName: "nft-marketplace",
    repoName: "marketplace-contracts",
    ref: "develop",
    fuzzer: "echidna",
    fuzzerArgs: {
      target: "test/echidna/",
      prepareContracts: [
        {
          target: "address(0x456)",
          replacement: "address(mockNFT)",
          targetContract: "MockERC721",
        },
      ],
    },
  },
  {
    id: "recipe-3",
    displayName: "Governance Token Analysis",
    orgName: "dao-governance",
    repoName: "governance-token",
    ref: "feature/voting-power",
    fuzzer: "foundry",
    fuzzerArgs: {
      target: "test/fuzz/",
      prepareContracts: [
        {
          target: "100000000",
          replacement: "MAX_SUPPLY",
          targetContract: "GovernanceToken",
        },
        {
          target: "address(0x789)",
          replacement: "address(treasury)",
          targetContract: "Treasury",
        },
      ],
    },
  },
];

export function useGetRecipes(): {
  data: Recipe[];
  isLoading: boolean;
  refetch: any;
  isRefetching: boolean;
} {
  // Without branch this doesn't really make sense
  return useQuery<Recipe[], string>({
    queryKey: ["getRecipes"],
    queryFn: async () => {
      try {
        // Return mock data instead of making API call
        console.log("getRecipes.data (mock)", mockRecipes);
        return mockRecipes;

        // Original API call (commented out for demo)
        // const res = await axios.get("/api/recipes");
        // console.log("getRecipes.data", res.data);
        // return res.data.data;
      } catch (e) {
        // return getABIMock;
        console.log("rev", e);
        throw "Unable to get projects";
      }
    },
  });
}

export function useGetRecipeByIdentifier(identifier: string): {
  data: Recipe;
  isLoading: boolean;
} {
  const query = useGetRecipes();

  const [recipe, setRecipe] = useState<Recipe>();

  useEffect(() => {
    if (query.data) {
      // Return first found
      const found = query.data.find(
        (toTest) => String(toTest.id) === String(identifier)
      );
      setRecipe(found);
    }
  }, [query.data, identifier]);

  return {
    ...query,
    data: recipe,
  };
}
