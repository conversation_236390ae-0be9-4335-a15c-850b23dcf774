import {
  extractEchidnaPace,
  extractLastElapsedTimeFromMedusa,
  extractLastTestCountFromEchidna,
  formatSecondsToDaysHoursMinutes,
} from "@/lib/utils";
import { Fuzzer, type BrokenProperty } from "@recon-fuzz/log-parser";
import { useQuery } from "@tanstack/react-query";
import axios from "axios";
import { useEffect, useState } from "react";

// import { getJobsMock } from "../mocks/getJobs";

export interface Job {
  id: string;
  orgName: string;
  repoName: string;
  ref: string;
  fuzzer: string;
  directory: string;
  taskArn?: string;
  corpusUrl?: string;
  coverageUrl?: string;
  logsUrl?: string;
  status: string;
  createdAt: string;
  updatedAt: string;
  fuzzerArgs: any;
  label?: string;

  metadata: {
    commit: string;
    method: string;
    startedBy: string;
  };
  testsDuration: string;
  testsCoverage: number;
  testsFailed: number;
  testsPassed: number;
  numberOfTests: number;
  brokenProperties: BrokenProperty[];
  progress?: number;
  eta?: string;
}

// Mock data for jobs
const mockJobs: Job[] = [
  {
    id: "job-1",
    orgName: "defi-protocol",
    repoName: "core-contracts",
    ref: "main",
    fuzzer: "medusa",
    directory: "test/invariants",
    taskArn: "arn:aws:ecs:us-east-1:123456789:task/abc123",
    corpusUrl: "https://s3.amazonaws.com/recon-corpus/job-1/corpus.tar.gz",
    coverageUrl: "https://s3.amazonaws.com/recon-coverage/job-1/coverage.html",
    logsUrl: "https://s3.amazonaws.com/recon-logs/job-1/logs.txt",
    status: "SUCCESS",
    createdAt: "2024-01-15T10:30:00Z",
    updatedAt: "2024-01-15T12:45:00Z",
    fuzzerArgs: {
      timeout: 3600,
      target: "test/invariants/",
      config: "medusa.json",
    },
    label: "DeFi Protocol Security Audit",
    metadata: {
      commit: "a1b2c3d4e5f6",
      method: "manual",
      startedBy: "security-team",
    },
    testsDuration: "2h 15m",
    testsCoverage: 87.5,
    testsFailed: 0,
    testsPassed: 156,
    numberOfTests: 156,
    brokenProperties: [],
    progress: 100,
    eta: null,
  },
  {
    id: "job-2",
    orgName: "nft-marketplace",
    repoName: "marketplace-contracts",
    ref: "develop",
    fuzzer: "echidna",
    directory: "test/echidna",
    taskArn: "arn:aws:ecs:us-east-1:123456789:task/def456",
    corpusUrl: "https://s3.amazonaws.com/recon-corpus/job-2/corpus.tar.gz",
    coverageUrl: "https://s3.amazonaws.com/recon-coverage/job-2/coverage.html",
    logsUrl: "https://s3.amazonaws.com/recon-logs/job-2/logs.txt",
    status: "ERROR",
    createdAt: "2024-01-16T14:20:00Z",
    updatedAt: "2024-01-16T15:30:00Z",
    fuzzerArgs: {
      testLimit: 100000,
      contract: "MarketplaceTester",
      config: "echidna.yaml",
    },
    label: "NFT Marketplace Vulnerability Scan",
    metadata: {
      commit: "f7g8h9i0j1k2",
      method: "webhook",
      startedBy: "github-actions",
    },
    testsDuration: "1h 10m",
    testsCoverage: 62.3,
    testsFailed: 3,
    testsPassed: 89,
    numberOfTests: 92,
    brokenProperties: [
      {
        brokenProperty: "invariant_price_never_zero",
        traces:
          "Transaction sequence that led to price being set to zero:\n1. setPrice(0)\n2. executeOrder(orderId)\nResult: Price became zero, violating invariant",
      },
      {
        brokenProperty: "invariant_balance_consistency",
        traces:
          "Balance inconsistency detected:\n1. deposit(1000)\n2. withdraw(500)\n3. transfer(600)\nResult: Total balance mismatch",
      },
    ],
    progress: 100,
    eta: null,
  },
  {
    id: "job-3",
    orgName: "dao-governance",
    repoName: "governance-token",
    ref: "feature/voting-power",
    fuzzer: "foundry",
    directory: "test/fuzz",
    taskArn: "arn:aws:ecs:us-east-1:123456789:task/ghi789",
    corpusUrl: null,
    coverageUrl: null,
    logsUrl: "https://s3.amazonaws.com/recon-logs/job-3/logs.txt",
    status: "RUNNING",
    createdAt: "2024-01-17T09:15:00Z",
    updatedAt: "2024-01-17T11:30:00Z",
    fuzzerArgs: {
      runs: 10000,
      seed: 42,
      verbosity: "-vvv",
    },
    label: "Governance Token Fuzz Testing",
    metadata: {
      commit: "l3m4n5o6p7q8",
      method: "scheduled",
      startedBy: "cron-job",
    },
    testsDuration: "2h 15m",
    testsCoverage: 73.8,
    testsFailed: 1,
    testsPassed: 234,
    numberOfTests: 235,
    brokenProperties: [
      {
        brokenProperty: "invariant_total_supply_constant",
        traces:
          "Total supply violation:\n1. mint(user1, 1000)\n2. burn(user2, 500)\n3. mint(user3, 200)\nResult: Total supply increased unexpectedly",
      },
    ],
    progress: 65,
    eta: "45m",
  },
];

export function useGetJobs(): {
  data: Job[];
  isLoading: boolean;
  refetch: any;
  isRefetching: boolean;
} {
  return useQuery<Job[], string>({
    queryKey: ["getJobs"],
    queryFn: async () => {
      try {
        // Return mock data instead of making API call
        console.log("getJobs.data (mock)", mockJobs);
        return mockJobs;

        // Original API call (commented out for demo)
        // const res = await axios.get("/api/jobs");
        // for (const job in res.data.data) {
        //   const currJob = res.data.data[job];
        //   try {
        //     const { progress, eta } = await getJobProgress(currJob);
        //     if (progress) {
        //       currJob.progress = progress;
        //     }
        //     if (eta) {
        //       currJob.eta = eta;
        //     }
        //   } catch (err) {
        //     console.log("error getting job progress ", err);
        //   }
        //   res.data.data[job] = currJob;
        // }
        // return res.data.data;
      } catch {
        // return getJobsMock;
        throw "Unable to get projects";
      }
    },
  });
}

export function useGetJobById(identifier: string): {
  data: Job;
  isLoading: boolean;
} {
  const query = useGetJobs();
  console.log("query.data", query.data);

  const [job, setJob] = useState<Job>();

  useEffect(() => {
    if (query.data) {
      // Return first found
      const project = query.data.find(
        (project) => String(project.id) === String(identifier)
      );

      setJob(project);
    }
  }, [query.data, identifier]);

  return {
    ...query,
    data: job,
  };
}

interface JobProgress {
  progress: number;
  eta: string | null;
}

export const getJobProgress = async (job: Job): Promise<JobProgress | null> => {
  if (
    job.status === "ERROR" ||
    job.status === "STOPPED" ||
    job.status === "SUCCESS"
  ) {
    return {
      progress: 100,
      eta: null,
    };
  }
  if (job.status === "STARTED" || job.status === "QUEUED") {
    return {
      progress: 0,
      eta: null,
    };
  }
  if (!job.logsUrl) {
    return {
      progress: 0,
      eta: null,
    };
  }
  if (job.fuzzer === Fuzzer.ECHIDNA) {
    const jobLogsRaw = await axios({
      method: "POST",
      url: `/api/fetchLogs`,
      data: {
        logsUrl: job?.logsUrl,
      },
    });
    const progress = extractLastTestCountFromEchidna(jobLogsRaw.data);
    const remainingSeconds = extractEchidnaPace(jobLogsRaw.data);
    if (remainingSeconds === 0) {
      return {
        progress: 100,
        eta: null,
      };
    }
    const etaString = formatSecondsToDaysHoursMinutes(remainingSeconds);
    return {
      progress,
      eta: etaString ? etaString : null,
    };
  } else if (job.fuzzer === Fuzzer.MEDUSA) {
    const jobLogsRaw = await axios({
      method: "POST",
      url: `/api/fetchLogs`,
      data: {
        logsUrl: job?.logsUrl,
      },
    });

    const timeoutInSeconds = job.fuzzerArgs?.timeout || 0;
    const elapsedSeconds = extractLastElapsedTimeFromMedusa(jobLogsRaw.data);

    const remainingSeconds = timeoutInSeconds - elapsedSeconds;
    if (remainingSeconds <= 0) {
      return {
        progress: 100,
        eta: null,
      };
    }

    const etaString = formatSecondsToDaysHoursMinutes(remainingSeconds);
    const progress = (elapsedSeconds / timeoutInSeconds) * 100;

    return {
      progress: Math.min(Math.max(Math.round(progress), 0), 100),
      eta: etaString,
    };
  } else {
    return {
      progress: 100,
      eta: null,
    };
  }
};
